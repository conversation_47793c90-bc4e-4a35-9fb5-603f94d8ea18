<template>
  <div id="accordion">
    <div class="card" v-for="(item, index) in data" :key="index">
      <div
        class="card-header"
        :class="{ selected: selectedItemId === item.ncm_proposto }"
        @click="expandCollapse(item)"
      >
        <button
          class="btn btn-link"
          data-toggle="collapse"
          :data-target="'#collapse' + item.ncm_proposto"
          aria-expanded="false"
          :aria-controls="'collapse' + item.ncm_proposto"
        >
          <div class="ncm-title-container">
            <span class="ncm-title mr-3">{{
              formatNCM(item.ncm_proposto)
            }}</span>
            <span
              data-toggle="tooltip"
              data-html="true"
              style="cursor: help"
              title="Atributos obrigatórios desta NCM"
            >
              <div
                class="circle blue-circle"
                :class="{ selected: selectedItemId === item.ncm_proposto }"
              ></div>
              <span>{{ item.total_atributos_obrigatorios }}</span>
            </span>
            <span
              data-toggle="tooltip"
              data-html="true"
              style="cursor: help"
              title="Atributos opcionais desta NCM"
            >
              <div
                class="circle"
                :class="{ selected: selectedItemId === item.ncm_proposto }"
              ></div>
              <span>{{ item.total_atributos_nao_obrigatorios }}</span>
            </span>
          </div>
          <div class="ncm-arrow-container">
            <span>{{ item.total_itens }} itens</span>
            <i
              class="glyphicon"
              :class="
                selectedItemId === item.ncm_proposto
                  ? 'glyphicon-chevron-up'
                  : 'glyphicon-chevron-down'
              "
            ></i>
          </div>
        </button>
      </div>

      <div
        v-if="selectedItemId === item.ncm_proposto"
        class="collapse"
        :class="{ show: selectedItemId === item.ncm_proposto }"
        :aria-labelledby="'heading' + item.ncm_proposto"
        data-parent="#accordion"
      >
        <div class="card-body" style="position: relative">
          <WfTabelaAtributos
            v-if="selectedItemId === item.ncm_proposto"
            :data="selectedItemId"
            :totalqtdItems="item.total_itens"
            @openModalDiscart="openModalDiscart"
            :saveData="saveData"
            @dataEdited="handleDataEdited"
            :homologarSemObrigatorios="homologarSemObrigatorios"
          >
          </WfTabelaAtributos>
        </div>
      </div>
    </div>

    <ModalDiscart
      v-if="modalNcmId"
      @closeCollapse="closeCollapse"
      @salvarItens="salvarItens"
      :selectedItemId="modalNcmId"
    ></ModalDiscart>
  </div>
</template>

<script>
import axios from 'axios';
import WfTabelaAtributos from './WfTabelaAtributos.vue';
import ModalDiscart from './components/modalDiscart.vue';

export default {
  components: {
    WfTabelaAtributos,
    ModalDiscart,
  },
  data() {
    return {
      hasBeenEdited: false,
      selectedItemId: null,
      modalNcmId: null,
      saveData: null
    };
  },
  props: {
    data: {
      required: true,
      default: String,
    },
    homologarSemObrigatorios: {
      required: false,
      default: Boolean|Number
    }
  },
  mounted() {
    $('[data-toggle="tooltip"]').tooltip();
    this.hasBeenEdited = false;

    // console.log('homologarSemObrigatorios', this.homologarSemObrigatorios);
  },
  methods: {
    async expandCollapse(item) {
      if (this.selectedItemId === item.ncm_proposto) {
        this.openModalDiscart();
      } else if (
        this.selectedItemId &&
        this.selectedItemId !== item.ncm_proposto
      ) {
        this.openModalDiscart();
        this.modalNcmId = item.ncm_proposto;
        this.expandCollapse(item);
      } else {
        this.selectedItemId = item.ncm_proposto;
        this.modalNcmId = item.ncm_proposto;
      }
    },
    handleDataEdited(value) {  // Método para lidar com o evento
      this.hasBeenEdited = value;
    },
    formatNCM(ncm) {
      ncm = ncm.replace(/\D/g, '');
      if (ncm.length !== 8) {
        throw new Error('O NCM deve ter exatamente 8 dígitos');
      }
      return ncm.replace(/(\d{4})(\d{2})(\d{2})/, '$1.$2.$3');
    },
    openModalDiscart() {
      
      if (this.hasBeenEdited)
      {
        $('#ModalDiscart').modal('show');
      } else {
         this.closeCollapse({ item: this.selectedItemId });
      }
     
    },
    salvarItens() {
      this.saveData = true;
      setTimeout(() => {
        this.closeCollapse({ item: this.selectedItemId });
        this.saveData = false;
      }, 2000);
    },
    closeCollapse({ item }) {
      this.hasBeenEdited = false;
      $('#ModalDiscart').modal('hide');
      if (this.selectedItemId === item) {
        this.selectedItemId = null;
        this.modalNcmId = null;
      } else if (this.selectedItemId && this.selectedItemId !== item) {
        this.selectedItemId = item;
        this.modalNcmId = item;
      }
      this.dadosAtributos = { lista: [], itens: {} };
    },
  },
};
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border: 1px solid #e2e3e5;
  margin: 10px 0;
  padding: 10px;

  &:hover {
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  }

  &.selected {
    background: #337ab7;
    color: white;
  }
}

.btn-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-decoration: none;
  color: inherit;

  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.ncm-title-container,
.ncm-arrow-container {
  display: flex;
  gap: 20px;
  align-items: center;
}
.ncm-title {
  font-size: 18px;
  font-weight: 500;
}
.circle {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: transparent;
  border-radius: 50%;
  border: 1px solid #337ab7;

  &.selected {
    border: 1px solid white;
  }
}
.blue-circle {
  background-color: #337ab7;

  &.selected {
    background-color: white;
  }
}
</style>
