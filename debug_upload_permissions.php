<?php
/**
 * Script para diagnosticar problemas de permissão de upload
 * Execute este script para verificar as permissões do diretório de upload
 */

// Definir o caminho que está sendo usado
$upload_path = '/var/www/html/assets/tmp/';

echo "=== DIAGNÓSTICO DE PERMISSÕES DE UPLOAD ===\n\n";

echo "Caminho testado: $upload_path\n\n";

// 1. Verificar se o diretório existe
echo "1. Verificando se o diretório existe:\n";
if (is_dir($upload_path)) {
    echo "   ✓ Diretório existe\n";
} else {
    echo "   ✗ Diretório NÃO existe\n";
    echo "   Tentando criar...\n";
    if (mkdir($upload_path, 0777, true)) {
        echo "   ✓ Diretório criado com sucesso\n";
    } else {
        echo "   ✗ Falha ao criar diretório\n";
        exit(1);
    }
}

// 2. Verificar permissões do diretório
echo "\n2. Verificando permissões do diretório:\n";
$perms = fileperms($upload_path);
$perms_octal = substr(sprintf('%o', $perms), -4);
echo "   Permissões atuais: $perms_octal\n";

// 3. Verificar proprietário e grupo
echo "\n3. Verificando proprietário e grupo:\n";
$stat = stat($upload_path);
$owner_uid = $stat['uid'];
$group_gid = $stat['gid'];

// Tentar obter nomes dos usuários/grupos
$owner_info = posix_getpwuid($owner_uid);
$group_info = posix_getgrgid($group_gid);

echo "   Proprietário: " . ($owner_info ? $owner_info['name'] : $owner_uid) . " (UID: $owner_uid)\n";
echo "   Grupo: " . ($group_info ? $group_info['name'] : $group_gid) . " (GID: $group_gid)\n";

// 4. Verificar usuário atual do PHP
echo "\n4. Verificando usuário do processo PHP:\n";
$current_user = posix_getpwuid(posix_geteuid());
$current_group = posix_getgrgid(posix_getegid());
echo "   Usuário PHP: " . $current_user['name'] . " (UID: " . $current_user['uid'] . ")\n";
echo "   Grupo PHP: " . $current_group['name'] . " (GID: " . $current_group['gid'] . ")\n";

// 5. Testar is_writable() nativo do PHP
echo "\n5. Testando is_writable() nativo:\n";
if (is_writable($upload_path)) {
    echo "   ✓ is_writable() retorna TRUE\n";
} else {
    echo "   ✗ is_writable() retorna FALSE\n";
}

// 6. Testar is_really_writable() do CodeIgniter
echo "\n6. Testando is_really_writable() do CodeIgniter:\n";

// Simular a função is_really_writable
function test_is_really_writable($file) {
    // Se estivermos em Unix com safe_mode desligado, usar is_writable
    if (DIRECTORY_SEPARATOR == '/' AND @ini_get("safe_mode") == FALSE) {
        return is_writable($file);
    }

    // Para Windows ou safe_mode ligado, tentar escrever um arquivo
    if (is_dir($file)) {
        $file = rtrim($file, '/').'/'.md5(mt_rand(1,100).mt_rand(1,100));

        if (($fp = @fopen($file, 'ab')) === FALSE) {
            return FALSE;
        }

        fclose($fp);
        @chmod($file, 0777);
        @unlink($file);
        return TRUE;
    }
    elseif ( ! is_file($file) OR ($fp = @fopen($file, 'ab')) === FALSE) {
        return FALSE;
    }

    fclose($fp);
    return TRUE;
}

if (test_is_really_writable($upload_path)) {
    echo "   ✓ is_really_writable() retorna TRUE\n";
} else {
    echo "   ✗ is_really_writable() retorna FALSE\n";
}

// 7. Testar criação de arquivo real
echo "\n7. Testando criação de arquivo real:\n";
$test_file = $upload_path . 'test_' . time() . '.txt';
$test_content = "Teste de escrita - " . date('Y-m-d H:i:s');

if (file_put_contents($test_file, $test_content) !== false) {
    echo "   ✓ Arquivo criado com sucesso: $test_file\n";
    
    // Verificar se consegue ler o arquivo
    if (file_get_contents($test_file) === $test_content) {
        echo "   ✓ Arquivo lido com sucesso\n";
    } else {
        echo "   ✗ Falha ao ler arquivo\n";
    }
    
    // Limpar arquivo de teste
    if (unlink($test_file)) {
        echo "   ✓ Arquivo de teste removido\n";
    } else {
        echo "   ✗ Falha ao remover arquivo de teste\n";
    }
} else {
    echo "   ✗ Falha ao criar arquivo\n";
    echo "   Erro: " . error_get_last()['message'] . "\n";
}

// 8. Verificar configurações do PHP
echo "\n8. Verificando configurações do PHP:\n";
echo "   safe_mode: " . (ini_get('safe_mode') ? 'ON' : 'OFF') . "\n";
echo "   open_basedir: " . (ini_get('open_basedir') ?: 'não definido') . "\n";
echo "   upload_tmp_dir: " . (ini_get('upload_tmp_dir') ?: 'padrão do sistema') . "\n";

// 9. Sugestões de correção
echo "\n=== SUGESTÕES DE CORREÇÃO ===\n";

if (!is_writable($upload_path)) {
    echo "1. Ajustar permissões do diretório:\n";
    echo "   sudo chmod 775 $upload_path\n";
    echo "   sudo chown www-data:www-data $upload_path\n\n";
    
    echo "2. Ou permissões mais permissivas (menos seguro):\n";
    echo "   sudo chmod 777 $upload_path\n\n";
    
    echo "3. Verificar se o usuário do Apache/Nginx tem acesso:\n";
    echo "   sudo usermod -a -G www-data \$(whoami)\n\n";
}

echo "4. Verificar configuração do Docker (se aplicável):\n";
echo "   - Verificar se volumes estão montados corretamente\n";
echo "   - Verificar se o usuário do container tem permissões adequadas\n\n";

echo "=== FIM DO DIAGNÓSTICO ===\n";
?>
