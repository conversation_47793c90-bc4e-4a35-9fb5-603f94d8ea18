<!DOCTYPE html>
<?php

function is_page_selected($page, $is_blank = FALSE)
{
    $CI = &get_instance();

    $controller = $CI->uri->segment(1);

    $current_page = $controller;

    if ($method = $CI->uri->segment(2)) {
        $current_page .= '/' . $method;

        if ($method = $CI->uri->segment(3)) {
            $current_page .= '/' . $method;
        }
    }

    if ($is_blank && !$CI->uri->uri_string()) {
        echo ' class="active"';
    } else if ($page == $current_page) {
        echo $page . '  class="active" ';
    }
}

$CI = &get_instance();
if (!isset($CI->empresa_model)) {
    $CI->load->model('empresa_model');
}

$company_logo_filename = NULL;

if (is_demo_user()) {
    $company_logo_filename = base_url("assets/img/header/logo.png");
}

if (!$company_logo_filename) {
    $where = [];
    $where = [
        'ativo' => 1
    ];

    $company_info = $CI->empresa_model->get_entry(sess_user_company(), $where);

    $company_logo_filename = $CI->empresa_model->get_logo_url($company_info->logo_filename);
}


if (!$company_logo_filename) {
    $company_logo_filename = base_url("assets/img/header/logo-placeholder.png");
}

?>
<?php

$empresa = $CI->empresa_model->get_entry(sess_user_company());
$campos_adicionais = explode("|", $empresa->campos_adicionais);
$funcoes_adicionais = explode("|", $empresa->funcoes_adicionais);
$has_owner = in_array('owner', $campos_adicionais);

?>
<html>

<head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title><?php if (ENVIRONMENT != 'production') {
                echo 'Homologação - ';
            } ?>Gestão Tarifária <?php echo isset($title) ? '&rsaquo; ' . $title : '' ?></title>
    <?php

    echo link_tag('assets/css/bootstrap.min.css?v=1');
    echo link_tag('assets/css/bootstrap-glyphicons.css?v=1');
    echo link_tag('assets/css/font-awesome.min.css');
    echo link_tag('assets/css/main.css?v=1');

    echo link_tag('assets/css/sweetalert.css');

    if (isset($stylesheets)) {
        echo $stylesheets;
    }

    ?>
    <link rel="shortcut icon" href="<?php echo base_url( 'assets/img/icon-becomex.png' ) ?>" type="image/x-icon">
    <link rel="icon" href="<?php echo base_url( 'assets/img/icon-becomex.png' ) ?>" type="image/x-icon">

    <link href="https://cdn.jsdelivr.net/npm/vue-loading-overlay@4/dist/vue-loading.css" rel="stylesheet">
    <script type="text/javascript" src="<?php echo base_url('assets/js/jquery.min.js') ?>"></script>
    <script type="text/javascript" src="<?php echo base_url("assets/js/bootstrap.min.js") ?>"></script>
    <script type="text/javascript" src="<?php echo base_url('assets/js/sweetalert.min.js') ?>"></script>

    <script type="text/javascript" src="<?php echo base_url('assets/js/holder.js') ?>"></script>
    <?php if (has_role('sysadmin')) { ?>
        <script type="text/javascript" src="<?php echo base_url('assets/js/controle_pendencias/admin.js') ?>"></script>
        <script type="text/javascript" src="<?php echo base_url('assets/js/controle_pendencias/user.js') ?>"></script>
    <?php } else if (!has_role('cliente_pmo')) { ?>
        <script type="text/javascript" src="<?php echo base_url('assets/js/controle_pendencias/user.js') ?>"></script>
    <?php } ?>

    <?php

    if (isset($javascript)) {
        echo $javascript;
    }

    ?>

    <script type="text/javascript" charset="utf-8">
        var $base_url = '<?php echo base_url() ?>';

        function toggle_checkbox(o) {
            var checkboxes = $("input[type=checkbox][data-toggle=true]");
            checkboxes.prop('checked', $(o).prop('checked')).trigger("change");
        }

        $(function() {
            $('*[data-toggle="tooltip"]').tooltip();
        });

        $(function() {
            $('.container-alerta-pendencias .top-bar').on('click', function() {
                if ($(this).parent().hasClass('opened')) {
                    $(this).parent().removeClass('opened');

                    $(this).find('.down-caret').addClass('glyphicon-chevron-up');
                    $(this).find('.down-caret').removeClass('glyphicon-chevron-down');

                } else {
                    $(this).parent().addClass('opened');

                    $(this).find('.down-caret').addClass('glyphicon-chevron-down');
                    $(this).find('.down-caret').removeClass('glyphicon-chevron-up');
                }
            });

            $(window).scroll(function() {

                if ($(window).scrollTop() + $(window).height() >= $('.footer-text').offset().top) {
                    bottom = $(window).scrollTop() + $(window).height() - $('footer').offset().top;
                    $('.float-bottom-alert').css('bottom', bottom + 'px');
                } else {
                    $('.float-bottom-alert').css('bottom', '0px');
                }

            });
        });
    </script>

    <style type="text/css">
        .popover {
            min-width: 350px;
            z-index: initial;
        }

        /*.modal-backdrop {
        z-index: initial;
    }*/ 
    @media (min-width: 992px) {
        #perguntasRespostas .pos-relative{
            position: relative;
        }
        #perguntasRespostas .pos-unset,
        #perguntasRespostas .pos-relative .v-select.overflow-liberado{
            position: unset;
        }
        #perguntasRespostas .pos-relative .v-select.overflow-liberado .vs__dropdown-menu{
            /* width: auto; */
            /* max-width: 900px; */
            /* overflow-x: hidden;  */
            padding: 5px 10px;
            max-width: calc(100% - 30px);
            left: 15px;
            
        }

        #perguntasRespostas .pos-relative .v-select .vs__selected-options{
            overflow-x: hidden;
            overflow-y: visible;
        }

        #perguntasRespostas .pos-relative .v-select .vs__selected-options span{
            text-wrap: nowrap;
        }

        #perguntasRespostas .pos-relative .v-select .vs__selected-options span + .vs__search{
            line-height: 0;
            height: 0;
            margin: 0;
            border: 0;
        }
    }
        #perguntasRespostas .pos-relative .v-select.overflow-liberado .vs__dropdown-menu li{
            text-wrap: wrap;
        }
    </style>

    <script>
        (function(i, s, o, g, r, a, m) {
            i['GoogleAnalyticsObject'] = r;
            i[r] = i[r] || function() {
                (i[r].q = i[r].q || []).push(arguments)
            }, i[r].l = 1 * new Date();
            a = s.createElement(o),
                m = s.getElementsByTagName(o)[0];
            a.async = 1;
            a.src = g;
            m.parentNode.insertBefore(a, m)
        })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

        ga('create', 'UA-86212554-3', 'auto');
        ga('send', 'pageview');
    </script>

    <?php if (config_item('force_oauth2_login') == true) : ?>
        <style>
            .segunda-barra {
                /* color: #FFFFFF; */
                margin-top: -2px;
                background: rgb(0,137,101);
                background: -moz-linear-gradient(90deg, rgba(0,137,101,1) 0%, rgba(0,64,130,1) 100%);
                background: -webkit-linear-gradient(90deg, rgba(0,137,101,1) 0%, rgba(0,64,130,1) 100%);
                background: linear-gradient(90deg, rgba(0,137,101,1) 0%, rgba(0,64,130,1) 100%);
                filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#008965',endColorstr='#004082',GradientType=1);
            }
            .text-segunda-barra {
                color: #FFFFFF !important;
            }
        </style>
    <?php endif; ?>
</head>

<body>
    <script>
        $(document).ready(function(){
            $.ajax({
                url: '<?php echo base_url("home/ajax_get_pendencias") ?>',
                success: function(r) {
                    console.log(r);
                    if (r == '') return false;

                    // var data = $.parseJSON(r);
                    var data = r;
                    // if (data.data > 0) {
                        $('#total_to_answer').text(data.data);
                    // }
                }
            });
        })
    </script>
    <input type="hidden" name="base_url" id="base_url" value="<?php echo base_url() ?>">
    <input type="hidden" id="lang" name="lang" value="<?php echo $this->session->userdata('site_lang'); ?>">
    <?php if (ENVIRONMENT != 'production') : ?>
        <style>
            .subnav {
                margin-top: 0px !important;
            }
        </style>
        <div class="alert alert-warning text-center" role="alert" style="margin-top: 50px; margin-bottom: 0px;">
            <strong>Atenção!</strong> Você está acessando o ambiente de <strong>HOMOLOGAÇÃO</strong>.
        </div>
    <?php endif; ?>

    <div id="wrap">

        <div class="navbar navbar-default navbar-fixed-top segunda-barra" role="navigation">
            <div class="navbar-collapse collapse expand-navbar-width justify-center" style="display: flex !important">
                <?php if (config_item('force_oauth2_login') == true) { ?>  
                    <!-- <a class="navbar-brand" href="<?php echo base_url() ?>">  -->
                    <a class="navbar-brand" href="<?php echo config_item('url_beconnect');?>"> 
                        <!-- <img src="<?php echo base_url('assets/img/header/logo-horizontal.png') ?>" height="28" /> -->
                        <img src="<?php echo base_url('assets/img/logo-beconnect.png') ?>" height="32" />
                    </a>
                <?php } else { ?>
                    <a class="navbar-brand" href="<?php echo base_url() ?>"> 
                    <!-- <a class="navbar-brand" href="<?php echo config_item('url_beconnect');?>">  -->
                        <img src="<?php echo base_url('assets/img/header/logo-horizontal.png') ?>" height="28" />
                        <!-- <img src="<?php echo base_url('assets/img/logo-beconnect.png') ?>" height="32" /> -->
                    </a>
                <?php } ?>
                <ul class="nav navbar-nav get-nav-width">
                    <li <?php is_page_selected("home", TRUE) ?>>
                        <a href="<?php echo base_url() ?>"><i class="glyphicon glyphicon-home"></i></a>
                    </li>

                    <?php if (has_role('consulta_diana')) { ?>
                        <li <?php is_page_selected("consulta_diana", TRUE) ?>>
                            <a href="<?php echo base_url("consulta_diana") ?>"><img src="<?php echo base_url('assets/img/diana/diana_icon.png') ?>" width="18" />
                            </a>
                        </li>
                    <?php } ?>

                    <li <?php is_page_selected("cockpit", TRUE) ?>>
                        <a href="<?php echo base_url('cockpit') ?>" style="display: list-item;"><i class="glyphicon glyphicon-stats"></i></a>
                    </li>

                    <?php if (customer_has_role('preencher_atributos', sess_user_id()) || customer_has_role('homologar_atributos', sess_user_id()) || customer_has_role('movimentar_itens', sess_user_id())) { ?>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                Homologação <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li <?php is_page_selected("homologacao", TRUE) ?>>
                                    <a href="<?php echo site_url("homologacao") ?>">Homologação</a>
                                </li>
                                <li <?php is_page_selected('wf/atributos', TRUE) ?>>
                                    <a href="<?php echo site_url("wf/atributos") ?>">
                                        Análise de Atributos
                                    </a>
                                </li>
                            </ul>
                        </li>
                    <?php  } else if(has_role('homologacao')){ ?>
                        <li <?php is_page_selected("homologacao", TRUE) ?>>
                             <a href="<?php echo site_url("homologacao") ?>">Homologação</a>
                        </li>
                    <?php  } ?> 

                    <?php if (has_role('booking_eletronico')) { ?>
                        <li <?php is_page_selected("booking") ?>>
                            <a href="<?php echo site_url('booking') ?>">Booking Eletrônico</a>
                        </li>
                    <?php } ?>

                    <?php if (customer_can('ii') && (has_role('monitor_ex') || has_role('monitor_ex_apuracao'))) : ?>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                Benefícios Fiscais <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (has_role('monitor_ex')) { ?>
                                    <li <?php is_page_selected('monitor_ex') ?>>
                                        <a href="<?php echo site_url("monitor_ex") ?>">
                                            Monitor de EX/FTA
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('monitor_ex_apuracao')) { ?>
                                    <li <?php is_page_selected('monitor_ex_apuracao_ganhos') ?>>
                                        <a href="<?php echo site_url("monitor_ex_apuracao_ganhos") ?>">
                                            Monitor de EX/FTA - Apuração de Ganhos
                                        </a>
                                    </li>
                                <?php } ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    <?php if (
                        has_role('ncm_cenario') || has_role('ncm_comparar') || has_role('ncm_consulta_itens_descricao') ||
                        has_role('ncm_consulta_estatisticas') || has_role('ncm_consulta_itens_grupo') ||
                        has_role('ncm_explorar') || has_role('ncm_pre_selecao') || has_role('ncm_consulta_ex_tarif')
                    ) : ?>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                NCM <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (has_role('ncm_cenario')) { ?>
                                    <li <?php is_page_selected("cenario") ?>>
                                        <a href="<?php echo site_url('cenario') ?>">
                                            Cenário Orig. vs Proposto
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('ncm_comparar')) { ?>
                                    <li <?php is_page_selected("comparar_ncm") ?>>
                                        <a href="<?php echo site_url('comparar_ncm') ?>">
                                            Comparar NCM's
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('ncm_consulta_itens_descricao')) { ?>
                                    <li <?php is_page_selected("consulta/itens-descricao") ?>>
                                        <a href="<?php echo site_url('consulta/itens-descricao') ?>">
                                            Consulta de Itens por Descrição
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('ncm_consulta_estatisticas')) { ?>
                                    <li <?php is_page_selected("base_estatisticas") ?>>
                                        <a href="<?php echo site_url('base_estatisticas') ?>">
                                            Consulta Estatísticas
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('ncm_consulta_itens_grupo')) { ?>
                                    <li <?php is_page_selected("consulta/itens-grupo-tarifario") ?>>
                                        <a href="<?php echo site_url('consulta/itens-grupo-tarifario') ?>">
                                            Consulta por Grupo
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('ncm_explorar')) { ?>
                                    <li <?php is_page_selected("explorar_ncm") ?>>
                                        <a href="<?php echo site_url('explorar_ncm') ?>">
                                            Explorar NCM's
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('ncm_pre_selecao')) { ?>
                                    <li <?php is_page_selected("vinculacao/pre_selecao") ?>>
                                        <a href="<?php echo site_url('vinculacao/pre_selecao') ?>">
                                            Pré-Seleção EX
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('ncm_consulta_ex_tarif')) { ?>
                                    <li <?php is_page_selected("consulta_ex_tarifario") ?>>
                                        <a href="<?php echo site_url('consulta_ex_tarifario') ?>">
                                            Consulta Ex-tarifário/NVE e demais atributos
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (company_can('lessin')) : ?>
                                    <li <?php is_page_selected("consulta_lessin") ?>>
                                        <a href="<?php echo site_url('consulta_lessin') ?>">
                                            Consulta LESSIN
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif ?>

                    <?php if (
                        has_role('arquivos_mestre') || has_role('arquivos_homologacao') || has_role('arquivos_booking') ||
                        has_role('arquivos_implementacao') || has_role('arquivos_sugestao')
                    ) { ?>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                Arquivos <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (has_role('arquivos_mestre')) { ?>
                                    <li <?php is_page_selected('upload') ?>>
                                        <a href="<?php echo site_url("upload") ?>">
                                            Enviar Mestre de Itens
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('arquivos_homologacao')) { ?>
                                    <li <?php is_page_selected('uploadnext') ?>>
                                        <a href="<?php echo site_url("uploadnext") ?>">
                                            Enviar Itens para Homologação
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('arquivos_booking')) { ?>
                                    <li <?php is_page_selected('uploadbooking') ?>>
                                        <a href="<?php echo site_url("uploadbooking") ?>">
                                            Enviar Itens para Booking Eletronico
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('arquivos_implementacao')) { ?>
                                    <li <?php is_page_selected('implementacao/upload') ?>>
                                        <a href="<?php echo site_url("implementacao/upload") ?>">
                                            Enviar Itens para Implementação
                                        </a>
                                    </li>
                                <?php } ?>

                                <?php if (has_role('arquivos_sugestao')) { ?>
                                    <li <?php is_page_selected('uploaddescricao') ?>>
                                        <a href="<?php echo site_url("uploaddescricao") ?>">
                                            Enviar Itens para Sugestão de Grupo Tarifário
                                        </a>
                                    </li>
                                <?php } ?>
                            </ul>
                        </li>
                    <?php } ?>
                    <?php 
                            if (customer_has_role('dados_tecnicos',sess_user_id())) { ?>
                        <?php if (customer_has_role('dados_tecnicos_geral',sess_user_id())) { ?>
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    Dados Técnicos <b class="caret"></b>
                                </a>
                                <ul class="dropdown-menu">
                                    <li <?php is_page_selected("atribuir_grupo") ?>>
                                        <a style="padding-right: 12px; padding-left: 12px" href="<?php echo site_url('atribuir_grupo') ?>">Por Empresa</a>
                                    </li>
                                    <li <?php is_page_selected("atribuir_grupo_geral") ?>>
                                        <a style="padding-right: 12px; padding-left: 12px" href="<?php echo site_url('geral_sla').'?reset_filters=1' ?>">Geral</a>
                                    </li>
                                </ul>
                            </li>
                        <?php }else{ ?>
                                <li <?php is_page_selected("atribuir_grupo") ?>>
                                    <a style="padding-right: 12px; padding-left: 12px" href="<?php echo site_url('atribuir_grupo') ?>">Dados Técnicos</a>
                                </li>
                        <?php } ?>
                    <?php } ?>

                    <?php if (
                        has_role('gerenciar_atribuicao') || has_role('gerenciar_auditoria_monitor') || has_role('gerenciar_auditoria_fator') ||
                        has_role('gerenciar_busca') || has_role('gerenciar_capitulos') || has_role('gerenciar_empresas') || has_role('gerenciar_cest') ||
                        has_role('gerenciar_fotos') || has_role('gerenciar_grupos_cadastro') || has_role('gerenciar_grupos_logs') ||
                        has_role('visualizar_hierarquia') || has_role('gerenciar_hierarquia') || has_role('gerenciar_logs') ||
                        has_role('gerenciar_mestre') || has_role('gerenciar_perfil') || has_role('gerenciar_score') || has_role('gerenciar_secao') ||
                        has_role('gerenciar_segmentos') || has_role('gerenciar_usuarios')
                    ) { ?>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                Gerenciar <b class="caret"></b>
                            </a>

                            <ul class="dropdown-menu">
                                <?php if (has_role('gerenciar_atribuicao')) : ?>
                                    <?php if (customer_can('cest')) : ?>
                                        <li <?php is_page_selected('cest') ?>><a href="<?php echo site_url("cest") ?>">Atribuição CEST</a></li>
                                    <?php endif; ?>
                                <?php endif ?>

                                <?php if (has_role('gerenciar_auditoria_monitor') || has_role('gerenciar_auditoria_fator')) : ?>
                                    <li class="dropdown dropdown-submenu" <?php is_page_selected('cadastros/grupotarifario') ?>>
                                        <a href="#" class="dropdown-toggle" data-toggle="dropdown"> Auditoria de NFe</a>
                                        </a>
                                        <ul class="dropdown-menu">
                                            <?php if (has_role('gerenciar_auditoria_monitor')) : ?>
                                                <li <?php is_page_selected("monitor") ?>>
                                                    <a href="<?php echo site_url('monitor') ?>">
                                                        Monitor de NFe
                                                    </a>
                                                </li>
                                            <?php endif ?>
                                            <?php /*
                                <li <?php is_page_selected('depara') ?>>
                                    <a href="<?php echo site_url("depara") ?>">
                                        Itens sem De para
                                    </a>
                                </li>

                                <li
                                    <?php is_page_selected('uploadpreco') ?>>
                                    <a href="<?php echo site_url("uploadpreco") ?>">
                                        Enviar tabela de preço
                                    </a>
                                </li>
                                */ ?>
                                            <?php if (has_role('gerenciar_auditoria_fator')) : ?>
                                                <li <?php is_page_selected("cadastros/fator_conversao") ?>>
                                                    <a href="<?php echo site_url("cadastros/fator_conversao") ?>">
                                                        Fator de Conversão
                                                    </a>
                                                </li>
                                            <?php endif ?>
                                        </ul>
                                    <?php endif ?>

                                    <?php if (has_role('gerenciar_busca')) : ?>
                                    <li <?php is_page_selected('busca') ?>><a href="<?php echo site_url("busca") ?>">Busca de Fotos</a></li>
                                <?php endif ?>

                                <?php if (has_role('gerenciar_capitulos')) { ?>
                                    <li <?php is_page_selected('cadastros/capitulos') ?>><a href="<?php echo site_url("cadastros/capitulos") ?>">Capítulos NCM</a></li>
                                <?php } ?>

                                <?php if (has_role('gerenciar_empresas')) { ?>
                                    <li <?php is_page_selected('cadastros/empresa') ?>><a href="<?php echo site_url("cadastros/empresa") ?>">Empresas</a></li>
                                <?php } ?>

                                <?php if (has_role('gerenciar_cest')) { ?>
                                    <?php if (customer_can('cest')) : ?>
                                        <li <?php is_page_selected('cest/explorar') ?>>
                                            <a href="<?php echo site_url('cest/explorar'); ?>">Explorar CEST</a>
                                        </li>
                                    <?php endif; ?>
                                <?php } ?>

                                <?php if (has_role('gerenciar_fotos')) { ?>
                                    <li <?php is_page_selected('issues') ?>><a href="<?php echo site_url("issues") ?>">Fotos c/ Problemas</a></li>
                                <?php } ?>

                                <?php if (has_role('sysadmin') || has_role('consultor')) : ?>
                                    <li <?php is_page_selected('grupo_perguntas') ?>><a href="<?php echo site_url("grupo_perguntas") ?>">Grupo de Perguntas</a></li>
                                <?php endif; ?>

                                <?php if (
                                    has_role('gerenciar_grupos_cadastro') || has_role('gerenciar_grupos_logs') ||
                                    has_role('visualizar_hierarquia') || has_role('gerenciar_hierarquia')
                                ) : ?>
                                    <li class="dropdown dropdown-submenu" <?php is_page_selected('cadastros/grupotarifario') ?>>
                                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">Grupos Tarifários</a>

                                        <ul class="dropdown-menu">

                                            <?php if (has_role('gerenciar_grupos_cadastro')) : ?>
                                                <li <?php is_page_selected('cadastros/grupotarifario') ?>>
                                                    <a href="<?php echo site_url("cadastros/grupotarifario") ?>">
                                                        Cadastro
                                                    </a>
                                                </li>
                                            <?php endif ?>

                                            <?php if (has_role('gerenciar_grupos_logs')) : ?>
                                                <li <?php is_page_selected('cadastros/logs_grupos') ?>>
                                                    <a href="<?php echo site_url("cadastros/logs_grupos") ?>">
                                                        Logs
                                                    </a>
                                                </li>
                                            <?php endif ?>
                                                    <?php if (has_role('gerenciar_perfil')) { ?>
                                                        <li <?php is_page_selected('cadastros/perfil') ?>><a href="<?php echo site_url("cadastros/perfil") ?>">Perfil de Usuário</a></li>
                                                    <?php } ?>

                                            <?php if (has_role('visualizar_hierarquia') || has_role('gerenciar_hierarquia')) : ?>
                                                <li <?php is_page_selected('hierarquia') ?>>
                                                    <a href="<?php echo site_url("hierarquia") ?>">
                                                        Hierarquia
                                                    </a>
                                                </li>
                                            <?php endif ?>
                                        </ul>
                                    </li>
                                <?php endif ?>

                                <?php if (company_can("lessin") && (has_role("sysadmin") || has_role("consultor"))) : ?>
                                    <li <?php is_page_selected('lessin') ?>><a href="<?php echo site_url("lessin") ?>">Lessin</a></li>
                                <?php endif ?>

                                <?php if (has_role('gerenciar_logs')) : ?>
                                    <li <?php is_page_selected('cadastros/logs') ?>><a href="<?php echo site_url("cadastros/logs") ?>">Logs de Aprovação</a></li>
                                <?php endif ?>

                                    <?php if (has_role('gerenciar_mestre')) { ?>
                                        <li <?php is_page_selected('cadastros/mestre_itens') ?>><a href="<?php echo site_url("cadastros/mestre_itens") ?>">Mestre de Itens</a></li>
                                    <?php } ?>
                                    <?php if (in_array('owner', $campos_adicionais)) : ?>
                                        <li>
                                            <a href="<?php echo config_item('url_cadastro_owner'); ?>" target="_blank">Owner</a>
                                        </li>
                                    <?php endif; ?>
                                    <?php if (config_item('diplay_menu_vinculacao') == 1 || config_item('diplay_menu_vinculacao') == 2) : ?>
                                        <li <?php is_page_selected('vinculacao') ?>>
                                            <a href="<?php echo site_url("vinculacao") ?>">Pendências de Atribuições</a>
                                        </li>
                                    <?php endif; ?>
                                    <?php if (has_role('gerenciar_perfil')) { ?>
                                        <li <?php is_page_selected('cadastros/perfil') ?>><a href="<?php echo site_url("cadastros/perfil") ?>">Perfil de Usuário</a></li>
                                    <?php } ?>

                                    <?php if (has_role('gerenciar_score')) { ?>
                                        <li <?php is_page_selected('relatorios/score') ?>><a href="<?php echo site_url("relatorios/score") ?>">Score de Aprovação</a></li>
                                    <?php } ?>
                                    <?php if (in_array('unidade_negocio', $campos_adicionais)) : ?>
                                        <li>
                                            <a href="<?php echo config_item('url_unidade_negocio'); ?>" target="_blank">Unidade de Negócio</a>
                                        </li>
                                        <li>
                                            <a href="<?php echo config_item('url_monitor_flat_file'); ?>" target="_blank">Monitor Flat File</a>
                                        </li>
                                    <?php endif; ?>
                                    <?php if (has_role('gerenciar_secao')) { ?>
                                        <li <?php is_page_selected('cadastros/capitulos/grupos') ?>><a href="<?php echo site_url("cadastros/capitulos/grupos") ?>">Seção do Capítulo</a></li>
                                    <?php } ?>

                                <?php if (has_role('gerenciar_segmentos')) { ?>
                                    <li <?php is_page_selected('cadastros/segmento') ?>><a href="<?php echo site_url("cadastros/segmento") ?>">Segmentos</a></li>
                                <?php } ?>

                                <?php if (has_role('gerenciar_usuarios') || has_role("consultor")) { ?>
                                    <li <?php is_page_selected('cadastros/usuario') ?>><a href="<?php echo site_url("cadastros/usuario") ?>">Usuários</a></li>
                                <?php } ?>
                                 <?php if (customer_has_role('gerenciar_paises',  sess_user_id())) { ?>
                                    <li <?php is_page_selected('/cadastros/pais/novo') ?>><a href="<?php echo site_url("/cadastros/pais") ?>">Países</a></li>
                                <?php } ?>
                                <?php if (in_array('exportar_diana', $funcoes_adicionais)) : ?>
                                    <li>
                                        <a href="<?php echo config_item('upload_atributos'); ?>" target="_blank">Upload de Atributos</a>
                                    </li>
                                <?php endif; ?>

                                
                                <?php if (customer_has_role('gerenciar_prioridades', sess_user_id())) : ?>
                                    <li>
                                        <a href="<?php echo site_url("cadastros/prioridades") ?>" >Prioridades</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php } ?>
                </ul>

                <ul class="nav navbar-nav navbar-right">

                    <?php if (has_role('sysadmin') || has_role('consultor')) : ?>
                        <li>
                            <a type="button" style="padding-right: 10px; padding-left: 5px; cursor: pointer;" data-toggle="modal" data-target="#perguntasRespostas" title="Realizar Perguntas">
                                <i class="glyphicon glyphicon-edit"></i>
                            </a>
                        </li>
                    <?php endif; ?>

                    <li>
                        <a href="<?php echo site_url('controle_pendencias') ?>" type="button" style="padding-right: 10px; padding-left: 5px;" title="Visualizar Perguntas Pendentes">
                            <i class="glyphicon glyphicon-share"></i>
                            <p class="pull-left badge progress-bar-danger" id="total_to_answer" style="position: absolute;left: 5px;bottom: 2px;">
                                <?php //echo $total_perguntas; ?>
                                <span class="spinner-border" style="width: 11px;height:11px;" role="status" aria-hidden="true"></span>
                            </p>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo site_url('controle_pendencias/perguntas') ?>" type="button" style="padding-right: 10px; padding-left: 5px;" title="Ver minhas perguntas realizadas">
                            <i class="glyphicon glyphicon-inbox"></i>
                        </a>
                    </li>

                    <?php
                    if (isset($this->empresa_model)) {
                        $this->empresa_model->clear_states();
                    }
                    ?>
                    <?php if (has_role('sysadmin') || has_role('becomex_pmo')) { ?>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" id="dropdown-menu-empresa" data-toggle="dropdown">
                                Empresa <b class="caret"></b><br />
                                <span style="color: #53DB58; text-transform: uppercase; font-size: 9px; position: absolute; margin-top: -8px"><?php echo strtok($company_info->nome_fantasia, " ") ?></span>
                            </a>

                            <ul class="dropdown-menu" id="menu-dropdown-disable">
                                <li style="padding: 10px;">
                                    <input class="form-control" id="filter-empresas-search" type="text" placeholder="Digite o nome da empresa" autofocus>
                                </li>
                                <?php
                                $CI->empresa_model->set_state('filter.order_by', 'nome_fantasia');
                                $CI->empresa_model->set_state('ativo', '1');
                                $empresas = $CI->empresa_model->get_entries();

                                foreach ($empresas as $empresa) :
                                ?>
                                    <li>
                                        <a href="<?php echo site_url("home/change_company/" . $empresa->id_empresa) ?>">
                                            <?php echo $empresa->nome_fantasia ?>
                                            <?php if ($empresa->id_empresa == sess_user_company()) : ?>
                                                <span class="glyphicon glyphicon-ok text-success"></span>
                                            <?php endif ?>
                                        </a>
                                    </li>
                                <?php endforeach ?>
                            </ul>
                        </li>
                    <?php } else if (has_role('engenheiro') || has_role('fiscal')) { ?>
                        <li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown"> Empresa <b class="caret"></b> <br /> <span style="color: #53DB58; text-transform: uppercase; font-size: 9px; position: absolute; margin-top: -8px"><?php echo strtok($company_info->nome_fantasia, " ") ?></span>
                            </a>
                            <ul class="dropdown-menu" id="menu-dropdown-disable">
                                <li style="padding: 10px;">
                                    <input class="form-control" id="filter-empresas-search" type="text" placeholder="Digite o nome da empresa" autofocus>
                                </li>
                                <?php
                                if (isset($this->empresa_model)) {
                                    $this->empresa_model->clear_states();
                                }
                                $this->usuario_model->set_state('filter.order_by', 'nome_fantasia');
                                $empresas = $CI->usuario_model->get_empresas_by_user(sess_user_id());

                                foreach ($empresas as $empresa) : ?>
                                    <li><a href="<?php echo site_url("home/change_company/" . $empresa->id_empresa) ?>">
                                            <?php echo $empresa->nome_fantasia ?>
                                            <?php if ($empresa->id_empresa == sess_user_company()) { ?>
                                                <span class="glyphicon glyphicon-ok text-success"></span>
                                            <?php } ?>
                                        </a></li>

                                <?php endforeach ?>
                            </ul>
                        </li>
                    <?php } ?>

                    <li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="glyphicon glyphicon-user"></i>
                            <?php echo substr(sess_user_nome(), 0,  10) ?>
                            <b class="caret"></b> </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a href="<?php echo site_url("configuracao/editar_perfil") ?>">
                                    <i class="glyphicon glyphicon-lock"></i>
                                    Editar Perfil
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo base_url('login/logout'); ?>">
                                    <i class="glyphicon glyphicon-log-out"></i>
                                    Sair
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <?php if (!empty($company_logo_filename)) { ?>

            <div class="subnav">
                <div class="logo-container container">

                    <div class="row">
                        <div class="col-md-9"></div>
                        <div class="col-md-3">
                            <div class="logo">
                                <img src="<?php echo $company_logo_filename ?>" style="max-width: 150px; max-height: 60px;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php } ?>

        <div class="container" id="main-content">
            <?php
            if (isset($this->breadcrumbs)) :

                echo $this->breadcrumbs->show();

            endif;
            ?>
            <?php echo $show_message ?>
            <?php echo $yield ?>
        </div>
    </div>
    <footer>
        <div class="container">
            <div class="row">
                <?php if (customer_can('atr') || customer_can('ii') || customer_can('ipi') || customer_can('nve') || customer_can('cest')) : ?>
                    <div class="float-bottom-alert container-alerta-pendencias">
                        <div class="top-bar">
                            <p class="pull-left"><span class="badge total" style="background: #F9F9F9;color: #d9534f;"></span> atribuições pendentes</p>
                            <i class="glyphicon glyphicon-chevron-up down-caret"></i>
                        </div>
                        <div class="body">
                            <?php if (customer_can('ii')) : ?>
                                <div class="alerta">
                                    <p><span class="badge ex_ii"></span> EX de II pendentes </p>
                                </div>
                            <?php endif; ?>
                            <?php if (customer_can('ipi')) : ?>
                                <div class="alerta">
                                    <p><span class="badge ex_ipi"></span> EX de IPI pendentes</p>
                                </div>
                            <?php endif; ?>
                            <?php if (customer_can('nve')) : ?>
                                <div class="alerta">
                                    <p><span class="badge nve"></span> NVE pendentes</p>
                                </div>
                            <?php endif; ?>
                            <?php if (customer_can('cest')) : ?>
                                <div class="alerta">
                                    <p><span class="badge cest"></span> CEST pendentes</p>
                                </div>
                            <?php endif; ?>

                            <?php if (customer_can('atr')) : ?>
                                <div class="alerta">
                                    <p><span class="badge atr"></span> Itens com Atributos do Catálogo </p>
                                </div>
                            <?php endif; ?>

                            <div class="alerta">
                                <a href="<?php echo site_url('vinculacao'); ?>" class="btn btn-success btn-block">
                                    Classificar itens
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
                    <a href="http://www.becomex.com.br/" target="_blank" rel="noopener noreferrer">
                        <img src="<?php echo base_url('assets/img/login/becomex-logo-inverse-new.png'); ?>" class="navbar-brand" alt="Becomex">
                    </a>
                </div>
                <?php
                    $versao = $this->version_model->get_version();
                ?>

                <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4 text-center">
                    <span class="footer-text">&copy; <?php echo date('Y') ?> Becomex  &ndash; Gestão Tarifária | Versão <strong> <?php echo $versao; ?></strong></span>
                </div>


                <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4 text-right">

                    <a href="https://www.facebook.com/BecomexConsulting" rel="noopener noreferrer" target="_blank">
                        <span class="fa-stack fa-md">
                            <i class="fa fa-circle fa-stack-2x"></i>
                            <i class="fa fa-facebook fa-stack-1x fa-inverse"></i>
                        </span>
                    </a>


                    <a href="http://www.linkedin.com/company/becomex-consulting" rel="noopener noreferrer" target="_blank">
                        <span class="fa-stack fa-md">
                            <i class="fa fa-circle fa-stack-2x"></i>
                            <i class="fa fa-linkedin fa-stack-1x fa-inverse"></i>
                        </span>
                    </a>

                    <a href="https://twitter.com/becomex" rel="noopener noreferrer" target="_blank">
                        <span class="fa-stack fa-md">
                            <i class="fa fa-circle fa-stack-2x"></i>
                            <i class="fa fa-twitter fa-stack-1x fa-inverse"></i>
                        </span>
                    </a>

                    <a href="https://plus.google.com/+BecomexBrConsulting/" rel="noopener noreferrer" target="_blank">
                        <span class="fa-stack fa-md">
                            <i class="fa fa-circle fa-stack-2x"></i>
                            <i class="fa fa-google-plus fa-stack-1x fa-inverse"></i>
                        </span>
                    </a>

                    <a href="http://www.youtube.com/BecomexConsulting" rel="noopener noreferrer" target="_blank">
                        <span class="fa-stack fa-md">
                            <i class="fa fa-circle fa-stack-2x"></i>
                            <i class="fa fa-youtube-play fa-stack-1x fa-inverse"></i>
                        </span>
                    </a>
                </div>

            </div>
        </div>
        <input type="hidden" id="current_page" value="<?php echo current_url() ?>">
    </footer>

    <div class="modal fade" id="modal-email" style="z-index: 1040">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <label>Desconheço o item</label>
                </div>
                <div class="modal-body">
                    <form method="POST" action="<?php echo site_url('controle_pendencias/sugerir'); ?>" id="form_email_share">
                        <div class="form-group">
                            <label>Digite o e-mail do usuário sugerido</label>
                            <div class="input-group">
                                <select class="form-control" name="email_share">
                                    <option disabled selected>Selecione um usuário responsável</option>
                                    <?php

                                    $this->usuario_model->set_state('filter.id_empresa', sess_user_company());

                                    $users = $this->usuario_model->get_entries();
                                    foreach ($users as $user) {
                                    ?>
                                        <option><?php echo $user->email; ?></option>
                                    <?php } ?>
                                </select>
                                <a href="javascript:void(0);" class="input-group-addon">
                                    <i class="glyphicon glyphicon-user"></i>
                                </a>
                            </div>
                            <div class="form-group" style="margin-top: 15px;">
                                <textarea rows="2" name="comment" class="form-control" placeholder="Adicione um comentário..."></textarea>
                            </div>
                        </div>
                        <button type="button" class="btn btn-default" data-dismiss="modal">Fechar</button>
                        <button id="save_email_share" class="btn btn-primary">Enviar</button>
                    </form>
                </div>
            </div>

        </div>

    </div>

    <div class="modal fade" id="perguntasPendentes" tabindex="-1" role="dialog" aria-labelledby="perguntasPendentes" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" style="display: inline !important" id="exampleModalLabel">Pendência de Respostas</h3>

                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="margin-top: 6px;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body min-height-pendencias-modal">
                    <div class="row">
                        <div class="col-md-12 text-center">
                            Olá <strong><?php echo sess_user_nome() ?></strong>, você tem algumas perguntas pendentes de respostas.
                        </div>

                        <div class="col-md-12 text-center">
                            <a href="<?php echo base_url("controle_pendencias") ?>">Clique aqui para acessar.</a>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn btn-default" data-dismiss="modal" aria-label="Close">
                        Fechar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php $this->load->view('atribuir_grupo/modal-perguntas-respostas', ['has_owner' => $has_owner]); ?>
</body>

</html>
<script type="text/javascript">
    (function(h, o, t, j, a, r) {
        h.hj = h.hj || function() {
            (h.hj.q = h.hj.q || []).push(arguments)
        };
        h._hjSettings = {
            hjid: 1179243,
            hjsv: 6
        };
        a = o.getElementsByTagName('head')[0];
        r = o.createElement('script');
        r.async = 1;
        r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    $(function() {
        if ($(document).height() <= $(window).height()) {
            $('.float-bottom-alert').css('bottom', '50px');
        }

        //
        <?php if ((config_item('diplay_menu_vinculacao') == 1 || config_item('diplay_menu_vinculacao') == 3) && $this->uri->uri_string !== 'vinculacao') { ?>
            setTimeout(function(){
                alerta_pendencias = $('.container-alerta-pendencias');
                ajax_pendencias = $.ajax({
                    url: '<?php echo base_url("home/ajax_get_vinculacao") ?>',
                    success: function(r) {
                        // console.log(r);
                        if (r == '') return false;

                        var data = $.parseJSON(r);
                        if (data.total_geral_alertas > 0) {
                            $(alerta_pendencias).find('.total').html(data.total_geral_alertas);
                            $(alerta_pendencias).find('.ex_ii').html(data.ex_ii?.pendencias);
                            $(alerta_pendencias).find('.ex_ipi').html(data.ex_ipi?.pendencias);
                            $(alerta_pendencias).find('.nve').html(data.nve?.pendencias);
                            $(alerta_pendencias).find('.cest').html(data.cest?.pendencias);
                            $(alerta_pendencias).find('.atr').html(data.attrs?.pendencias);
                            $(alerta_pendencias).fadeIn();
                        }
                    }
                });

                $(window).on('beforeunload', function() {
                    if (ajax_pendencias.readyState != 4) {
                        ajax_pendencias.abort();
                    }
                });
            },12000)
            
        <?php } ?>

        let showModalPerguntasPendentes = "<?php echo $this->session->userdata('perguntas_pendentes') ? 's' : 'n'; ?>";
        let totalPerguntasPendentes = "<?php echo $total_perguntas; ?>";

        if (showModalPerguntasPendentes == 's' && totalPerguntasPendentes > 0) {
            $("#perguntasPendentes").modal("show");
        }

        $("#perguntasPendentes").on("shown.bs.modal", function() {
            <?php $this->session->unset_userdata('perguntas_pendentes'); ?>
        });
    });

    // $(".filter-empresas-search").on("click", function(e)
    // {
    //     e.preventDefault();
    // });
    $('#menu-dropdown-disable').bind('click', function(e) {
        e.stopPropagation();
    });

    // $('#dropdown-menu-empresa').on('click', function(){
    //     setTimeout(function() { $('#filter-empresas-search').focus() }, 3000);
    //     console.log('olá amigos');
    // })

    $("#filter-empresas-search").on("input", function() {
        var input, filter, ul, li, a, i;
        input = document.getElementById("filter-empresas-search");
        filter = input.value.toUpperCase();
        div = document.getElementById("menu-dropdown-disable");
        a = div.getElementsByTagName("a");
        for (i = 0; i < a.length; i++) {
            if (a[i].innerHTML.toUpperCase().indexOf(filter) > -1) {
                a[i].style.display = "";
            } else {
                a[i].style.display = "none";
            }
        }

    });
</script>
<style type="text/css">
    .justify-center {
        display: flex !important;
        justify-content: center;
    }

    /* .modal-backdrop {
        z-index: 1029 !important;
    } */

    .min-height-pendencias-modal {
        min-height: 150px;
        display: flex;
        align-items: center;
    }
</style>

<!-- Render time: {elapsed_time} -->