<?php

class MY_Controller extends CI_Controller
{
	var $js_data 	= array();
	var $css_data 	= array();

	var $js_path  = NULL;
	var $css_path = NULL;

    public $_total_perguntas = 0;

    public $_total_geral_alertas = 0;
    public $_ex_ii = 0;
    public $_ex_ipi = 0;
    public $_nve = 0;
	public $_attrs = 0;

	public function __construct()
	{
		parent::__construct();

		$this->js_path = base_url() . 'assets/js/';
		$this->css_path = base_url() . 'assets/css/';

		$this->load->model(array(
			'usuario_model', 'ctr_pendencias_pergunta_model', 'version_model'
		));

	// $this->_total_perguntas = $this->ctr_pendencias_pergunta_model->getTotalPendencias();

		$this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
		$search_part_number = $this->ctr_pendencias_pergunta_model->get_state('filter.partnumbers');
		$remover_busca_estabelecimento = true;
		$partNumbersPerguntas = [];
		// var_dump($this->input->is_ajax_request());
		
		// $partNumbersPerguntas = $this->ctr_pendencias_pergunta_model->getPartnumbersComPerguntasPendentes(null,null, false,$search_part_number,$remover_busca_estabelecimento,TRUE);
		

		$total = 0;
		foreach ( $partNumbersPerguntas  as $p)
		{
			$total = $total + $p->total;
		}
		$this->_total_perguntas = $total;

		
		if ($this->_total_perguntas == 0) {
			$this->session->unset_userdata('perguntas_pendentes');
		}

        // if (is_logged())
        // {
        //     if (has_role('sysadmin') || has_role('becomex_pmo'))
        //     {
        //         // $this->ctr_pendencias_pergunta_model->set_state('filter.id_empresa', sess_user_company());

        //         if (has_role('becomex_pmo') && !has_role('sysadmin'))
        //         {
        //             // $this->ctr_pendencias_pergunta_model->set_state('get_from_all_owners', TRUE);
        //         }else{
        //             // $this->ctr_pendencias_pergunta_model->set_state('get_from_all_owners', FALSE);
        //         }

        //         // $this->_total_perguntas = $this->ctr_pendencias_pergunta_model->count_questions_owner_to_answer(sess_user_id());

        //     }else
        //     {
        //         if (is_logged())
        //         {
        //         }
        //     }
        // }

		if (file_exists(APPPATH . 'config/maintenance_mode')) {
            include(APPPATH . 'views/maintenance.php');
            exit();
        }

        $this->layout = 'layouts/default';
	}

	function include_js($jsfile)
	{
		if (is_array($jsfile))
		{
			foreach ($jsfile as $js)
			{
				$this->js_data[] = sprintf("<script type=\"text/javascript\" src=\"%s%s\"></script>", $this->js_path, $js);
			}
		}
		else
		{
			$this->js_data[] = sprintf("<script type=\"text/javascript\" src=\"%s%s\"></script>", $this->js_path, $jsfile);
		}
	}

	function include_css($cssfile)
	{
		if (is_array($cssfile))
		{
			foreach ($cssfile as $css)
			{
				$this->css_data[] = sprintf("<link rel=\"stylesheet\" media=\"screen\" type=\"text/css\" href=\"%s%s?version=%s\" />\n", $this->css_path, $css, 1);
			}
		}
		else {
			$this->css_data[] = sprintf("<link rel=\"stylesheet\" media=\"screen\" type=\"text/css\" href=\"%s%s?version=%s\" />\n", $this->css_path, $cssfile, 1);
		}
	}

	function render($view = 'index', $options = NULL)
	{
        if (isset($this->title))
		{
			$layout_data['title'] = $this->title;
		}

		$layout_data['show_message']= $this->show_alert_message();
		$layout_data['yield'] 		= $this->load->view($view, $options, TRUE);
		$layout_data['stylesheets'] = implode("\r\n\t\t", $this->css_data);
		$layout_data['javascript'] 	= implode("\r\n\t\t", $this->js_data);
		$layout_data['total_perguntas'] = $this->_total_perguntas;

		$layout_data['total_geral_alertas'] = $this->_total_geral_alertas;
		$layout_data['itens_ex_ii'] = $this->_ex_ii;
		$layout_data['itens_ex_ipi'] = $this->_ex_ipi;
		$layout_data['itens_nve'] = $this->_nve;
		$layout_data['itens_attrs'] = $this->_attrs;

		$this->load->view($this->layout, $layout_data);

		$this->session->set_userdata('last_url', current_full_url());
	}

    function set_back_link() {
        $this->session->set_userdata('back_listing_link', current_full_url());
    }

    function redirect_url($url = NULL)
    {
        $final_url = $this->session->userdata('back_listing_link') ? $this->session->userdata('back_listing_link') : $url;

        redirect($final_url);
    }

	function message_config($message, $type = "success")
	{
		$content = '';

		if ($type == "success" || $type == "error" || $type == "warning")
		{
			if ($type == "error") {
				$type = "danger";
			}

			$content = '
			<div style="word-break: break-word;" class="alert alert-'.$type.' alert-dismissable">
				<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
				'.$message.'
			</div>
			';
		}

		return $content;
	}

	function message_on_render($message, $type)
	{
		$content = $this->message_config($message, $type);

		if (!isset($this->display_message)) {
			$this->display_message = '';
		}

		$this->display_message .= $content;
	}

	function message_next_render($message, $type = 'success', $disable_message_config = FALSE)
	{
		if (!$disable_message_config) {
			$content = $this->message_config($message, $type);
		} else {
			$content = $message;
		}

		if ($content) {
			$this->session->set_flashdata('message_on_render', $content);
		}

		return $content;
	}

	public function show_alert_message()
	{
		if (isset($this->display_message)) {
			return $this->display_message;
		}

		if ($this->session->flashdata('message_on_render')) {
			return $this->session->flashdata('message_on_render');
		}
	}
}
